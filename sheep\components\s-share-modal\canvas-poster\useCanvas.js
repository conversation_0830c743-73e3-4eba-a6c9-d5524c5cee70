/**
 * Shopro + qs-canvas 绘制海报
 * @version 1.0.0
 * <AUTHOR>
 * @param {Object} options - 海报参数
 * @param {Object} vm - 自定义组件实例
 */

import sheep from '@/sheep';
import QSCanvas from 'qs-canvas';
import {
  getPosterData
} from './poster';

export default async function useCanvas(options, vm) {
  const width = options.width;
  const qsc = new QSCanvas({
      canvasId: options.canvasId,
      width: options.width,
      height: options.height,
      setCanvasWH: (canvas) => {
        options.height = canvas.height;
      },
    },
    vm,
  );

  // 支持异步获取海报数据
  let drawer;
  try {
    drawer = await getPosterData(options);
  } catch (error) {
    console.error('获取海报数据失败:', error);
    throw new Error('海报数据获取失败，请稍后重试');
  }
  //这里可以获取到背景图
  // 绘制背景图
  let background;
  try {
    background = await qsc.drawImg({
      type: 'image',
      val: drawer.background,
      x: 0,
      y: 0,
      width,
      mode: 'widthFix',
      zIndex: 0,
    });
  } catch (error) {
    console.error('背景图绘制失败:', error);
    throw new Error('背景图加载失败，请检查网络连接');
  }
  await qsc.updateCanvasWH({
    width: background.width,
    height: background.bottom,
  });

  let list = drawer.list;

  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    // 绘制文字
    if (item.type === 'text') {
      await qsc.drawText(item);
    }
    // 绘制图片
    if (item.type === 'image') {
      try {
        if (item.d) {
          qsc.setCircle({
            x: item.x,
            y: item.y,
            d: item.d,
            clip: true,
          });
        }

        if (item.r) {
          qsc.setRect({
            x: item.x,
            y: item.y,
            height: item.height,
            width: item.width,
            r: item.r,
            clip: true,
          });
        }
        await qsc.drawImg(item);
        qsc.restore();
      } catch (error) {
        console.error(`图片绘制失败 (${item.name}):`, error);
        qsc.restore(); // 确保恢复画布状态

        // 特殊处理微信小程序码加载失败的情况
        if (item.name === 'wxacode') {
          console.warn('微信小程序码获取失败，可能是因为小程序未发布。跳过二维码绘制，继续生成海报其他内容。');
          // 可以在这里添加占位图或提示文字
          // 暂时跳过，不影响海报生成
        } else {
          console.error(`普通图片 ${item.name} 加载失败:`, error);
        }
        // 继续执行，不中断整个海报生成过程
      }
    }

    // 绘制二维码
    if (item.type === 'qrcode') {
      await qsc.drawQrCode(item);
    }
  }

  await qsc.draw();

  // 使用 Promise 包装延迟执行，确保能正确返回结果
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      try {
        options.src = await qsc.toImage();
        console.log('海报图片生成成功');
        resolve(options);
      } catch (error) {
        console.error('海报图片转换失败:', error);
        options.error = '海报图片转换失败';
        reject(error);
      }
    }, 100);
  });
}