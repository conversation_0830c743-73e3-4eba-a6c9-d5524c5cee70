import request from '@/sheep/request';

export default {
	// 招租列表
	schoolList: (params) =>
		request({
			url: 'meal.classes/classes_list',
			method: 'GET',
			params,
			custom: {
				showLoading: false,
			},
		}),
	//课程详情
	classInfo: (id) =>
		request({
			url: 'meal.classes/detail',
			method: 'GET',
			params: {
				id: id,
			},
		}),
	//收藏
	classesCollect: (data) =>
		request({
			url: 'meal.classes/collect',
			method: 'POST',
			data,
		}),
	// detail: (id, user_coupon_id) =>
	//   request({
	//     url: 'coupon/detail',
	//     method: 'GET',
	//     params: {
	//       id: id,
	//       user_coupon_id,
	//     },
	//   }),

};