<template>
  <su-fixed bottom placeholder bg="bg-white">
    <view class="ui-tabbar-box">
      <view class="ui-tabbar ss-flex ss-col-center ss-row-between">

        <view v-if="serviceIcon" class="detail-tabbar-item ss-flex ss-flex-col ss-row-center ss-col-center"
          style="margin-left: 12rpx;" @tap="backHome">
          <image class="item-icon" src="https://xsyczb.0rui.cn/my_img/hom.png" mode="aspectFit"></image>
          <view class="item-title">首页</view>
        </view>

        <view v-if="collectIcon" class="detail-tabbar-item ss-flex ss-flex-col ss-row-center ss-col-center"
          @tap="onFavorite">
          <block v-if="modelValue.favorite">
            <image class="item-icon" src="https://xsyczb.0rui.cn/my_img/star1.png"
              mode="aspectFit"></image>
            <view class="item-title">已收藏</view>
          </block>
          <block v-else>
            <image class="item-icon" src="https://xsyczb.0rui.cn/my_img/star.png"
              mode="aspectFit"></image>
            <view class="item-title">收藏</view>
          </block>
        </view>
        <view v-if="gwcShow" class="detail-tabbar-item ss-flex ss-flex-col ss-row-center ss-col-center"
          style="margin-left: 12rpx;" @tap="backGw">
          <image class="item-icon" src="https://xsyczb.0rui.cn/my_img/gw.png" mode="aspectFit"></image>
          <view class="item-title">购物车</view>
        </view>

        <!-- <view v-if="shareIcon" class="detail-tabbar-item ss-flex ss-flex-col ss-row-center ss-col-center"
          style="margin-right: 12rpx;" @tap="showShareModal">
          <image class="item-icon" :src="sheep.$url.static('/assets/addons/shopro/uniapp/goods/share.png')"
            mode="aspectFit"></image>
          <view class="item-title">分享</view>
        </view> -->
        <slot></slot>
      </view>
    </view>
  </su-fixed>
</template>

<script setup>
/**
 *
 * 底部导航
 *
 * @property {String} bg 			 			- 背景颜色Class
 * @property {String} ui 			 			- 自定义样式Class
 * @property {Boolean} noFixed 		 			- 是否定位
 * @property {Boolean} topRadius 		 		- 上圆角
 *
 *
 */

import { computed, reactive } from 'vue';
import sheep from '@/sheep';
import { showShareModal } from '@/sheep/hooks/useModal';

const userInfo = computed(() => sheep.$store('user').userInfo);

// 数据
const state = reactive({});

// 接收参数
const props = defineProps({
  modelValue: {
    type: Object,
    default() { },
  },
  bg: {
    type: String,
    default: 'bg-white',
  },
  bgStyles: {
    type: Object,
    default() { },
  },
  ui: {
    type: String,
    default: '',
  },

  noFixed: {
    type: Boolean,
    default: false,
  },
  topRadius: {
    type: Number,
    default: 0,
  },
  collectIcon: {
    type: Boolean,
    default: true,
  },
  serviceIcon: {
    type: Boolean,
    default: true,
  },
  shareIcon: {
    type: Boolean,
    default: true,
  },
  gwcShow:{
    type: Boolean,
    default: true,
  }
});
const elStyles = computed(() => {
  return {
    'border-top-left-radius': props.topRadius + 'rpx',
    'border-top-right-radius': props.topRadius + 'rpx',
    overflow: 'hidden',
  };
});

const tabbarheight = (e) => {
  uni.setStorageSync('tabbar', e);
};
async function onFavorite() {
  const { code } = await sheep.$api.user.favorite.do(props.modelValue.id);
  if (code === 1) {
    if (props.modelValue.favorite) {
      props.modelValue.favorite = 0;
    } else {
      props.modelValue.favorite = 1;
    }
  }
}

// const onChat = () => {
//   sheep.$router.go('/pages/chat/index', {
//     id: props.modelValue.id,
//   });
// };

//联系平台客服
function onChat() {
  console.log('联系平台客服', userInfo.value.platform_contact_number);
  wx.makePhoneCall({
    phoneNumber: userInfo.value.platform_contact_number,
    success: function () {
      console.log("拨打电话成功")

    },
    fail: function (err) {
      console.log("拨打电话失败", err)
    }
  })
}


//返回首页
function backHome() {
  uni.switchTab({
    url: '/pages/index/index'
  })
}
function backGw() {
  uni.navigateTo({
    url: '/pages/index/cart'
  })
}

</script>

<style lang="scss" scoped>
.ui-tabbar-box {
  box-shadow: 0px -6px 10px 0px rgba(51, 51, 51, 0.2);
}

.ui-tabbar {
  display: flex;
  height: 50px;
  background: #fff;

  .detail-tabbar-item {
    width: 90rpx;

    .item-icon {
      width: 40rpx;
      height: 40rpx;
    }

    .item-title {
      font-size: 20rpx;
      font-weight: 500;
      line-height: 20rpx;
      margin-top: 12rpx;
    }
  }
}
</style>
