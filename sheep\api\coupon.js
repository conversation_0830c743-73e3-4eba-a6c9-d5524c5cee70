import request from '@/sheep/request';

export default {
	// 优惠券
	list: (params) =>
		request({
			url: 'coupon',
			method: 'GET',
			params,
			custom: {
				showLoading: false,
			},
		}),
	userCoupon: (params) =>
		request({
			url: 'user.coupon',
			method: 'GET',
			params,
		}),
	userTimesList: (params) =>
		request({
			url: 'meal.score/mobile_list',
			method: 'GET',
			params,
		}),
	detail: (id, user_coupon_id) =>
		request({
			url: 'coupon/detail',
			method: 'GET',
			params: {
				id: id,
				user_coupon_id,
			},
		}),
	get: (id) =>
		request({
			url: 'coupon/get',
			method: 'POST',
			params: {
				id: id,
			},
		}),
	listByGoods: (id) =>
		request({
			url: 'coupon/listByGoods',
			method: 'GET',
			params: {
				goods_id: id,
			},
		}),
	useTimes: (data) =>
		request({
			url: 'meal.score/mobile_use',
			method: 'POST',
			data,
		}),
	registerCoupon: (params) =>
		request({
			url: 'coupon/register_giftCoupons_list',
			method: 'GET',
			params,
		}),
	registerCouponReceive: (data) =>
		request({
			url: 'coupon/get_register_giftCoupons',
			method: 'POST',
			data,
		}),
};