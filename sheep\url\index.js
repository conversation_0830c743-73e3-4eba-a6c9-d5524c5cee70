import $store from '@/sheep/store';
import { staticUrl } from '@/sheep/config';
import { append_thumbnail_params } from './resize';

// 添加cdn域名前缀
export const _cdn = (url = '', cdnurl = '') => {
  if (!url) return '';
  if (url.indexOf('http') === 0) {
    return url;
  }
  if (cdnurl === '') {
    cdnurl = $store('app').info.cdnurl;
  }

  // 检查CDN域名是否有效
  if (!cdnurl || cdnurl === 'undefined' || cdnurl === 'null') {
    console.warn('CDN域名未配置或无效:', cdnurl, '原始URL:', url);
    // 如果CDN域名无效，检查是否是相对路径
    if (url.startsWith('/')) {
      return url; // 返回相对路径，让浏览器自动处理
    }
    return url; // 返回原始URL
  }

  // 确保CDN域名以/结尾，URL不以/开头时自动添加
  const normalizedCdnUrl = cdnurl.endsWith('/') ? cdnurl : cdnurl + '/';
  const normalizedUrl = url.startsWith('/') ? url.substring(1) : url;

  return normalizedCdnUrl + normalizedUrl;
};


// 对象存储自动剪裁缩略图
export const _thumb = (url = '', params) => {
  url = _cdn(url);
  return append_thumbnail_params(url, params);
}

// 静态资源地址
export const _static = (url = '', staticurl = '') => {
  if (staticurl === '') {
    staticurl = staticUrl;
  }
  if (staticurl !== 'local') {
    url = _cdn(url, staticurl);
  }
  return url;
}

// css背景图片地址
export const _css = (url = '', staticurl = '') => {
  if (staticurl === '') {
    staticurl = staticUrl;
  }
  if (staticurl !== 'local') {
    url = _cdn(url, staticurl);
  }
  // #ifdef APP-PLUS
  if (staticurl === 'local') {
    url = plus.io.convertLocalFileSystemURL(url);
  }
  // #endif
  return `url(${url})`;
}

export default {
  cdn: _cdn,
  static: _static,
  css: _css,
  thumb: _thumb
}
